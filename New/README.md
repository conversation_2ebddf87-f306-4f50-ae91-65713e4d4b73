# DermaCare Data Sync Service

A modern, bi-directional data synchronization service between CliniCore (CC) and AutoPatient (AP) platforms, built for Cloudflare Workers with TypeScript.

## 🎯 Overview

This service replaces the legacy v3Integration socket-based system with a modern webhook-based architecture. It maintains complete bi-directional data synchronization while providing improved performance, reliability, and maintainability.

### Key Features

- **Bi-directional Sync**: Complete data synchronization between CC and AP platforms
- **Webhook-based**: HTTP webhooks replace socket.io for better reliability
- **Buffer Management**: Intelligent duplicate prevention with configurable time windows
- **TypeScript**: Strict typing throughout with no 'any' types
- **Cloudflare Workers**: Optimized for edge computing and global distribution
- **Performance**: All webhook requests complete within 25 seconds
- **Error Handling**: Comprehensive error logging and recovery mechanisms

## 🏗️ Architecture

### Data Flow Overview

```
CC Platform ←→ DermaCare Sync Service ←→ AP Platform
    ↓                    ↓                    ↓
Patients            Buffer Manager        Contacts
Appointments        Error Logger          Appointments
Invoices            Database              Custom Fields
Payments            API Clients          Notes
```

### Sync Operations

#### CC → AP Flow
1. **Patients**: CC patients sync to AP contacts with custom field mapping
2. **Appointments**: CC appointments create/update AP calendar events
3. **Invoices/Payments**: Financial data syncs to AP custom fields (LTV, payment status)

#### AP → CC Flow
1. **Contacts**: AP contacts sync to CC patients
2. **Appointments**: AP calendar events create/update CC appointments

### Webhook Endpoints

| Endpoint | Method | Purpose | Source |
|----------|--------|---------|---------|
| `/webhook` | POST | Main CC webhook receiver | CliniCore |
| `/ap/appointment` | POST | AP appointment webhooks | AutoPatient |
| `/ap/contact` | POST | AP contact webhooks | AutoPatient |
| `/health` | GET | Health check with buffer stats | Monitoring |
| `/sync-services` | POST | Service sync endpoint | Legacy compatibility |

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm
- Cloudflare account with Workers enabled
- PostgreSQL database (Neon recommended)
- CC and AP API credentials

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd New

# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Sync database schema
pnpm run db:sync

# Start development server
pnpm run dev
```

### Environment Configuration

Create a `.env` file with the following variables:

```env
# Database
DATABASE_URL=postgresql://user:password@host:port/database

# CliniCore API
CC_API_DOMAIN=https://your-cc-domain.com/api
CC_API_KEY=your-cc-api-key

# AutoPatient API
AP_API_DOMAIN=https://services.leadconnectorhq.com
AP_API_KEY=your-ap-api-key
LOCATION_ID=your-ap-location-id
AP_CALENDAR_ID=your-ap-calendar-id

# Buffer Configuration
BUFFER_DURATION_SECONDS=60
BUFFER_TOLERANCE_SECONDS=60

# Development
NODE_ENV=development
```

## 🛠️ Development

### Development Server

```bash
# Start Wrangler dev server
pnpm run dev

# Start with ngrok tunnel (recommended for webhook testing)
pnpm run dev:tunnel
```

### Database Operations

```bash
# Generate new migration
npx drizzle-kit generate

# Push schema changes
npx drizzle-kit push

# Combined sync (generate + push)
pnpm run db:sync
```

### Code Quality

```bash
# Lint code
pnpm run lint

# Fix linting issues
pnpm run lint:fix

# Type checking
npx tsc --noEmit
```

## 🧪 Testing

### Webhook Testing

Use the included webhook testing utility:

```bash
# Test all endpoints on local development
node webhook-tester.js local all

# Test specific webhook types
node webhook-tester.js local cc-webhooks
node webhook-tester.js local ap-webhooks

# Test with ngrok tunnel
node webhook-tester.js ngrok all

# Performance testing
node webhook-tester.js local all --performance

# Stress testing
node webhook-tester.js local stress
```

### Manual Testing

```bash
# Health check
curl http://localhost:8787/health

# Test CC webhook
curl -X POST http://localhost:8787/webhook \
  -H "Content-Type: application/json" \
  -d '{"event":"EntityWasCreated","model":"Patient","id":123,"payload":{...}}'

# Test AP webhook
curl -X POST http://localhost:8787/ap/contact \
  -H "Content-Type: application/json" \
  -d '{"event":"contact.created","data":{"contact":{...}}}'
```

## 📊 Database Schema

### Core Tables

#### `patients`
- Primary sync table linking CC patients to AP contacts
- Stores both CC and AP data for comparison and conflict resolution
- Tracks update timestamps from both platforms

#### `appointments`
- Links CC appointments to AP calendar events
- Maintains bidirectional relationship data
- Includes AP note IDs for appointment details

#### `ap_custom_fields` / `cc_custom_fields`
- Caches custom field configurations from both platforms
- Enables efficient custom field mapping and updates

#### `error_logs`
- Comprehensive error tracking and debugging
- Includes stack traces, context data, and error types
- Enables monitoring and troubleshooting

### Relationships

```sql
patients (1) ←→ (many) appointments
patients.id = appointments.patient_id

ap_custom_fields (many) ←→ (1) configuration
cc_custom_fields (many) ←→ (1) configuration
```

## 🔧 Configuration

### Buffer Management

The buffer system prevents duplicate processing of events:

```typescript
// Default configuration
BUFFER_DURATION_SECONDS=60    // How long to keep items in buffer
BUFFER_TOLERANCE_SECONDS=60   // Additional tolerance for timing
```

### Custom Field Mapping

Custom fields are automatically mapped between platforms:

```typescript
// AP Payment Custom Fields
const AP_PAYMENT_CUSTOM_FIELDS = {
  LatestPaymentStatus: "Latest Payment Status",
  LatestAmountPaid: "Latest Amount Paid",
  LatestPaymentDate: "Latest Payment Date",
  LatestPaymentPDFURL: "Latest Payment PDF URL"
};

// AP LTV Custom Fields
const AP_LTV_CUSTOM_FIELDS = {
  LTV: "LTV",
  TotalPaidAmount: "Total Paid Amount"
};
```

### API Rate Limiting

Both CC and AP APIs have rate limits. The service includes:

- Request queuing and throttling
- Exponential backoff for failed requests
- Circuit breaker patterns for API failures

## 🚨 Error Handling

### Error Types

1. **Validation Errors**: Invalid webhook payloads or missing required fields
2. **API Errors**: External API failures or rate limiting
3. **Database Errors**: Connection issues or constraint violations
4. **Processing Errors**: Business logic failures or data conflicts

### Error Recovery

- Automatic retry with exponential backoff
- Dead letter queue for failed events
- Manual retry capabilities through admin interface
- Comprehensive error logging for debugging

### Monitoring

```bash
# Check error logs
curl http://localhost:8787/health

# View recent errors in database
SELECT * FROM error_logs ORDER BY created_at DESC LIMIT 10;
```

## 🔄 Data Transformation

### CC Patient → AP Contact

```typescript
{
  email: ccPatient.email,
  phone: ccPatient.phoneMobile,
  firstName: ccPatient.firstName,
  lastName: ccPatient.lastName,
  dateOfBirth: ccPatient.dob,
  gender: ccPatient.gender,
  tags: ["cc_api"],
  source: "cc"
}
```

### AP Contact → CC Patient

```typescript
{
  firstName: apContact.firstName,
  lastName: apContact.lastName,
  email: apContact.email,
  phoneMobile: apContact.phone,
  customFields: [...] // Mapped from AP custom fields
}
```

### Appointment Mapping

Both directions maintain:
- Start/end times with timezone conversion
- Patient/contact relationships
- Appointment status and notes
- Third-party source tracking (prevents loops)

## 🚀 Deployment

### Cloudflare Workers

```bash
# Deploy to production
pnpm run deploy

# Deploy with specific environment
wrangler deploy --env production

# View deployment logs
wrangler tail
```

### Environment Setup

1. **Database**: Set up PostgreSQL database (Neon recommended)
2. **Secrets**: Configure Cloudflare Workers secrets
3. **Webhooks**: Configure webhook URLs in CC and AP platforms
4. **Monitoring**: Set up health check monitoring

### Production Configuration

```bash
# Set production secrets
wrangler secret put DATABASE_URL
wrangler secret put CC_API_KEY
wrangler secret put AP_API_KEY

# Configure custom domains
wrangler route add "sync.yourdomain.com/*" your-worker-name
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Webhook Not Receiving Events

**Symptoms**: No webhook events being processed

**Solutions**:
- Check webhook URL configuration in CC/AP platforms
- Verify ngrok tunnel is active for local development
- Check firewall and network connectivity
- Validate webhook endpoint authentication

```bash
# Test webhook connectivity
curl -X POST https://your-webhook-url/health
```

#### 2. Database Connection Errors

**Symptoms**: `Database connection failed` errors

**Solutions**:
- Verify DATABASE_URL is correctly configured
- Check database server status and connectivity
- Ensure database credentials are valid
- Run database migrations if needed

```bash
# Test database connection
pnpm run db:sync
```

#### 3. API Authentication Failures

**Symptoms**: `401 Unauthorized` or `403 Forbidden` errors

**Solutions**:
- Verify API keys are current and valid
- Check API key permissions and scopes
- Ensure location ID is correct for AP API
- Test API credentials independently

```bash
# Test CC API
curl -H "Authorization: Bearer YOUR_CC_API_KEY" \
  https://your-cc-domain.com/api/patients

# Test AP API
curl -H "Authorization: Bearer YOUR_AP_API_KEY" \
  https://services.leadconnectorhq.com/contacts/
```

#### 4. Buffer/Duplicate Processing Issues

**Symptoms**: Events being processed multiple times or skipped

**Solutions**:
- Check buffer configuration settings
- Verify system clock synchronization
- Review buffer timeout settings
- Clear buffer if needed (restart service)

#### 5. Performance Issues

**Symptoms**: Webhook timeouts or slow processing

**Solutions**:
- Check database query performance
- Review API response times
- Optimize custom field processing
- Monitor memory usage

```bash
# Performance testing
node webhook-tester.js local all --performance
```

### Debug Mode

Enable verbose logging for troubleshooting:

```typescript
// Set in environment or code
DEBUG=true
LOG_LEVEL=debug
```

### Health Monitoring

The `/health` endpoint provides system status:

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "buffer": {
    "size": 42,
    "maxSize": 1000
  },
  "database": "connected",
  "apis": {
    "cc": "healthy",
    "ap": "healthy"
  }
}
```

## 📚 API Reference

### Webhook Event Formats

#### CC Webhook Events

```typescript
interface CCWebhookEvent {
  event: "EntityWasCreated" | "EntityWasUpdated" | "EntityWasDeleted" |
         "AppointmentWasCreated" | "AppointmentWasUpdated" | "AppointmentWasDeleted";
  model: "Patient" | "Appointment" | "Invoice" | "Payment" | "Service";
  id: number;
  payload: CCPatientType | CCAppointmentType | InvoiceType | PaymentType;
}
```

#### AP Webhook Events

```typescript
interface APWebhookEvent {
  event: "contact.created" | "contact.updated" | "contact.deleted" |
         "appointment.created" | "appointment.updated" | "appointment.deleted";
  data: {
    contact?: APContactType;
    calendar?: APAppointmentType;
    contact_id?: string;
  };
}
```

### Response Formats

#### Success Response

```typescript
interface SuccessResponse {
  message: string;
  requestId: string;
  result?: {
    success: boolean;
    message: string;
    [key: string]: any;
  };
  operationType?: "create" | "update" | "delete";
}
```

#### Error Response

```typescript
interface ErrorResponse {
  error: string;
  requestId: string;
  details?: any;
}
```

## 🤝 Contributing

### Development Workflow

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** changes: `git commit -m 'Add amazing feature'`
4. **Push** to branch: `git push origin feature/amazing-feature`
5. **Open** a Pull Request

### Code Standards

- **TypeScript**: Strict mode with no `any` types
- **Formatting**: Use Biome for consistent formatting
- **Documentation**: JSDoc comments for all public functions
- **Testing**: Include tests for new functionality
- **Performance**: Ensure webhook processing under 25 seconds

### Pull Request Guidelines

- Include comprehensive description of changes
- Add tests for new functionality
- Update documentation as needed
- Ensure all CI checks pass
- Request review from maintainers

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- **Documentation**: Check this README and inline code comments
- **Issues**: Create GitHub issues for bugs and feature requests
- **Testing**: Use the webhook-tester.js utility for debugging
- **Monitoring**: Check the `/health` endpoint for system status

## 🔗 Related Projects

- **v3Integration**: Legacy socket-based implementation
- **CliniCore API**: Medical practice management platform
- **AutoPatient API**: Patient engagement and communication platform
- **Cloudflare Workers**: Serverless computing platform
