{"name": "data-sync", "type": "module", "scripts": {"dev": "wrangler dev --port 8787", "dev:tunnel": "concurrently \"npm run dev\" \"npm run ngrok\"", "ngrok": "ngrok http --url=proven-moose-hopefully.ngrok-free.app 8787", "ngrok:start": "ngrok http 8787", "deploy": "wrangler deploy --minify", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "db:sync": "npx drizzle-kit generate && npx drizzle-kit push", "db:generate": "npx drizzle-kit generate", "db:push": "npx drizzle-kit push", "db:studio": "npx drizzle-kit studio", "test": "node webhook-tester.js local all", "test:ngrok": "node webhook-tester.js ngrok all", "test:performance": "node webhook-tester.js local all --performance", "test:stress": "node webhook-tester.js local stress", "lint": "biome check .", "lint:fix": "biome check . --write", "type-check": "tsc --noEmit", "clean": "rm -rf dist .wrangler", "setup": "npm install && npm run db:sync"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "drizzle-orm": "^0.44.3", "drizzle-zod": "^0.8.2", "hono": "^4.8.5", "zod": "^4.0.5"}, "devDependencies": {"@biomejs/biome": "2.1.2", "drizzle-kit": "^0.31.4", "typescript": "^5.8.3", "wrangler": "^4.4.0"}}