import type {
	GetCCAppointmentType,
	GetCCPatientType,
	GetPaymentType,
	WebhookContext,
	WebhookEvent,
} from "@type";
import { isItInBuffer } from "@utils/bufferManager";
import { logError } from "@utils/errorLogger";
import { startRequestTimer, endRequestTimer, startTimer, endTimer } from "@utils/performanceMonitor";
import type { Context } from "hono";
import {
	processAppointmentCreate,
	processAppointmentDelete,
	processAppointmentUpdate,
} from "../processors/appointmentProcessor";
import { processInvoicePayment } from "../processors/invoicePaymentProcessor";
import {
	processPatientCreate,
	processPatientUpdate,
} from "../processors/patientProcessor";

/**
 * Main webhook event handler for CC (CliniCore) platform events
 *
 * This is the primary entry point for all webhook events from the CliniCore platform.
 * It replaces the socket.io event handling from v3Integration with a modern HTTP
 * webhook-based approach that's compatible with Cloudflare Workers.
 *
 * **Supported Event Types:**
 * - `EntityWasCreated` - New entities (Patient, Invoice, Payment)
 * - `EntityWasUpdated` - Updated entities (Patient)
 * - `EntityWasDeleted` - Deleted entities (future implementation)
 * - `AppointmentWasCreated` - New appointments
 * - `AppointmentWasUpdated` - Updated appointments
 * - `AppointmentWasDeleted` - Deleted appointments
 *
 * **Processing Flow:**
 * 1. Parse and validate incoming webhook payload
 * 2. Generate unique request ID for tracking
 * 3. Create processing context with metadata
 * 4. Check buffer to prevent duplicate processing
 * 5. Route event to appropriate processor based on event type and model
 * 6. Return standardized response with processing results
 * 7. Log any errors for monitoring and debugging
 *
 * **Error Handling:**
 * - Validates webhook structure before processing
 * - Implements comprehensive error logging
 * - Returns appropriate HTTP status codes
 * - Provides detailed error messages for debugging
 *
 * **Performance Considerations:**
 * - Designed to complete within 25-second Cloudflare Workers timeout
 * - Uses efficient buffer management for duplicate prevention
 * - Optimized database queries and API calls
 * - Implements proper async/await patterns
 *
 * **Security:**
 * - Validates webhook payload structure
 * - Implements request ID tracking for audit trails
 * - Logs all processing attempts for security monitoring
 *
 * @param c - Hono context object containing request data and response methods
 * @param c.req - HTTP request object with webhook payload
 * @param c.json - Method to return JSON responses
 *
 * @returns Promise resolving to HTTP Response object
 * @returns response.status - HTTP status code (200 for success, 400/500 for errors)
 * @returns response.body - JSON response with processing results
 * @returns response.body.message - Human-readable processing message
 * @returns response.body.requestId - Unique request identifier for tracking
 * @returns response.body.result - Detailed processing results from processor
 * @returns response.body.skipped - Boolean indicating if event was skipped (duplicate)
 * @returns response.body.error - Error message if processing failed
 *
 * @throws {Error} Critical errors are caught and logged, but function returns error response
 *
 * @example
 * ```typescript
 * // Example webhook payload from CC
 * const webhookPayload = {
 *   event: "EntityWasCreated",
 *   model: "Patient",
 *   id: 12345,
 *   payload: {
 *     id: 12345,
 *     firstName: "John",
 *     lastName: "Doe",
 *     email: "<EMAIL>",
 *     updatedAt: "2024-01-01T12:00:00Z"
 *   }
 * };
 *
 * // Handler processes the webhook
 * const response = await handleWebhookEvent(honoContext);
 *
 * // Example success response
 * {
 *   message: "Webhook processed successfully",
 *   requestId: "req_abc123",
 *   result: {
 *     success: true,
 *     message: "Patient created successfully in AP",
 *     patientId: "ap_contact_456"
 *   }
 * }
 * ```
 *
 * @see {@link routeWebhookEvent} for event routing logic
 * @see {@link isValidWebhookEvent} for payload validation
 * @see {@link generateBufferKey} for buffer key generation
 *
 * @since 1.0.0
 * @version 1.0.0
 */
export async function handleWebhookEvent(c: Context): Promise<Response> {
	const requestId = crypto.randomUUID();

	// Start performance tracking for the entire request
	const requestTimer = startRequestTimer(requestId, "webhook_processing");

	try {
		// Parse webhook payload
		const parseTimer = startTimer("webhook_payload_parse", requestId);
		const webhookEvent: WebhookEvent = await c.req.json();
		endTimer(parseTimer, true);

		// Validate webhook event structure
		if (!isValidWebhookEvent(webhookEvent)) {
			await logError(
				"WEBHOOK_VALIDATION_ERROR",
				new Error("Invalid webhook event structure"),
				{ webhookEvent, requestId },
				"WebhookHandler",
			);
			return c.json(
				{ error: "Invalid webhook event structure", requestId },
				400,
			);
		}

		// Create processing context
		const context: WebhookContext = {
			event: webhookEvent,
			processedAt: new Date(),
			requestId,
		};

		// Check buffer to prevent duplicate processing
		const bufferKey = generateBufferKey(webhookEvent);
		if (await isItInBuffer(bufferKey)) {
			console.log(
				`Event ${webhookEvent.event}:${webhookEvent.model}:${webhookEvent.id} is in buffer, skipping processing`,
			);
			return c.json(
				{
					message: "Event already processed recently",
					requestId,
					skipped: true,
				},
				200,
			);
		}

		// Route to appropriate processor based on event and model
		const processingTimer = startTimer("webhook_event_processing", requestId);
		const result = await routeWebhookEvent(context);
		endTimer(processingTimer, true);

		// End performance tracking
		const totalMetrics = endRequestTimer(requestTimer);

		return c.json(
			{
				message: "Webhook processed successfully",
				requestId,
				result,
				performance: totalMetrics ? {
					totalDuration: totalMetrics.totalDuration,
					operationCount: totalMetrics.operations.length
				} : undefined,
			},
			200,
		);
	} catch (error) {
		// End performance tracking on error
		endRequestTimer(requestTimer);

		await logError(
			"WEBHOOK_PROCESSING_ERROR",
			error,
			{ requestId },
			"WebhookHandler",
		);

		return c.json(
			{
				error: "Internal server error",
				requestId,
			},
			500,
		);
	}
}

/**
 * Validates webhook event structure to ensure it conforms to the expected format
 *
 * This function performs comprehensive validation of incoming webhook events to ensure
 * they contain all required fields with correct types before processing.
 *
 * @param event - Unknown event data received from webhook
 * @returns True if the event is a valid WebhookEvent, false otherwise
 *
 * @example
 * ```typescript
 * const incomingData = await request.json();
 * if (isValidWebhookEvent(incomingData)) {
 *   // Safe to process as WebhookEvent
 *   await processWebhookEvent(incomingData);
 * } else {
 *   // Invalid event structure
 *   return errorResponse("Invalid webhook event structure");
 * }
 * ```
 */
function isValidWebhookEvent(event: unknown): event is WebhookEvent {
	return (
		event !== null &&
		typeof event === "object" &&
		"event" in event &&
		"model" in event &&
		"id" in event &&
		"payload" in event &&
		typeof (event as Record<string, unknown>).event === "string" &&
		typeof (event as Record<string, unknown>).model === "string" &&
		typeof (event as Record<string, unknown>).id === "number" &&
		typeof (event as Record<string, unknown>).payload === "object" &&
		(event as Record<string, unknown>).payload !== null
	);
}

/**
 * Generates a unique buffer key for the webhook event to prevent duplicate processing
 *
 * Creates a composite key using event type, model, and ID to uniquely identify
 * each webhook event for buffer management and duplicate detection.
 *
 * @param event - Webhook event containing event type, model, and ID
 * @returns Buffer key string in format "event:model:id"
 *
 * @example
 * ```typescript
 * const event = {
 *   event: "EntityWasCreated",
 *   model: "Patient",
 *   id: 123,
 *   payload: { ... }
 * };
 * const key = generateBufferKey(event);
 * // Returns: "EntityWasCreated:Patient:123"
 * ```
 */
function generateBufferKey(event: WebhookEvent): string {
	return `${event.event}:${event.model}:${event.id}`;
}

/**
 * Routes webhook events to appropriate processors based on event type and model
 *
 * This function acts as a central dispatcher, analyzing the webhook event type
 * and routing it to the correct processing function. It handles all supported
 * event types including entity creation, updates, deletions, and appointment-specific events.
 *
 * @param context - Webhook processing context containing event data and metadata
 * @returns Promise resolving to processing result with success status and message
 *
 * @throws {Error} When an unsupported event type is encountered
 *
 * @example
 * ```typescript
 * const context = {
 *   event: {
 *     event: "EntityWasCreated",
 *     model: "Patient",
 *     id: 123,
 *     payload: patientData
 *   },
 *   processedAt: new Date(),
 *   requestId: "req-123"
 * };
 *
 * const result = await routeWebhookEvent(context);
 * // Returns: { success: true, message: "Patient created successfully" }
 * ```
 */
async function routeWebhookEvent(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	try {
		switch (event.event) {
			case "EntityWasCreated":
				return await handleEntityCreated(context);

			case "EntityWasUpdated":
				return await handleEntityUpdated(context);

			case "EntityWasDeleted":
				return await handleEntityDeleted(context);

			case "AppointmentWasCreated":
				return await handleAppointmentCreated(context);

			case "AppointmentWasUpdated":
				return await handleAppointmentUpdated(context);

			case "AppointmentWasDeleted":
				return await handleAppointmentDeleted(context);

			default:
				throw new Error(`Unsupported event type: ${event.event}`);
		}
	} catch (error) {
		await logError(
			"WEBHOOK_ROUTING_ERROR",
			error,
			{ event: event.event, model: event.model, id: event.id },
			"WebhookRouter",
		);
		throw error;
	}
}

/**
 * Handles EntityWasCreated events
 */
async function handleEntityCreated(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	switch (event.model) {
		case "Patient":
			return await processPatientCreate(
				event.payload as GetCCPatientType,
				context,
			);

		case "Invoice":
		case "Payment":
			return await processInvoicePayment(
				event.payload as GetPaymentType,
				context,
			);

		case "Service":
			console.log("Service events are not processed", event);
			return { message: "Service events are not processed", skipped: true };

		default:
			console.log("Unknown EntityWasCreated model:", event.model);
			return { message: `Unknown model: ${event.model}`, skipped: true };
	}
}

/**
 * Handles EntityWasUpdated events
 */
async function handleEntityUpdated(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	switch (event.model) {
		case "Patient":
			return await processPatientUpdate(
				event.payload as GetCCPatientType,
				context,
			);

		case "Appointment":
			return await processAppointmentUpdate(
				event.payload as GetCCAppointmentType,
				context,
			);

		case "Service":
			console.log("Service events are not processed", event);
			return { message: "Service events are not processed", skipped: true };

		default:
			console.log("Unknown EntityWasUpdated model:", event.model);
			return { message: `Unknown model: ${event.model}`, skipped: true };
	}
}

/**
 * Handles EntityWasDeleted events
 */
async function handleEntityDeleted(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	switch (event.model) {
		case "Appointment":
			return await processAppointmentDelete(event.id, context);

		default:
			console.log("Unknown EntityWasDeleted model:", event.model);
			return { message: `Unknown model: ${event.model}`, skipped: true };
	}
}

/**
 * Handles AppointmentWasCreated events
 */
async function handleAppointmentCreated(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	if (event.model === "Appointment") {
		return await processAppointmentCreate(
			event.payload as GetCCAppointmentType,
			context,
		);
	}

	return {
		message: `Unexpected model for AppointmentWasCreated: ${event.model}`,
		skipped: true,
	};
}

/**
 * Handles AppointmentWasUpdated events
 */
async function handleAppointmentUpdated(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	if (event.model === "Appointment") {
		return await processAppointmentUpdate(
			event.payload as GetCCAppointmentType,
			context,
		);
	}

	return {
		message: `Unexpected model for AppointmentWasUpdated: ${event.model}`,
		skipped: true,
	};
}

/**
 * Handles AppointmentWasDeleted events
 */
async function handleAppointmentDeleted(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	if (event.model === "Appointment") {
		return await processAppointmentDelete(event.id, context);
	}

	return {
		message: `Unexpected model for AppointmentWasDeleted: ${event.model}`,
		skipped: true,
	};
}
