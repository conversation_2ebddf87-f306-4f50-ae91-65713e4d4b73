import { getDb } from "@database";
import { patient as patientSchema } from "@database/schema";
import type { GetCCPatientType } from "@type";
import { eq } from "drizzle-orm";
import { getConfig } from "./configs";

/**
 * In-memory buffer to track recent processing events
 * Replaces the Skip table from v3Integration with a simpler approach
 * Uses Map for O(1) lookup performance
 */
const processingBuffer = new Map<string, number>();

/**
 * Checks if an event is currently in the processing buffer to prevent duplicate processing
 *
 * This function provides an asynchronous interface to check if a specific event
 * is currently being processed or was recently processed within the buffer time window.
 * It helps prevent duplicate processing of webhook events that might be sent multiple times.
 *
 * @param bufferKey - Unique key identifying the event (format: "operation:entityType:id")
 * @returns Promise resolving to true if event is in buffer (should skip), false if not in buffer (should process)
 *
 * @example
 * ```typescript
 * const bufferKey = "EntityWasCreated:Patient:123";
 * const inBuffer = await isItInBuffer(bufferKey);
 *
 * if (inBuffer) {
 *   console.log("Event already processed recently, skipping");
 *   return { success: true, message: "Duplicate event skipped" };
 * }
 *
 * // Process the event
 * await processEvent(eventData);
 * ```
 */
export function isItInBuffer(bufferKey: string): Promise<boolean> {
	return Promise.resolve(isInBufferSync(bufferKey));
}

/**
 * Synchronous version of buffer check for internal use with automatic cleanup
 *
 * Performs the actual buffer checking logic, including automatic cleanup of expired
 * entries and validation of buffer timestamps against the configured buffer duration.
 *
 * @param bufferKey - Unique key identifying the event
 * @returns True if the event is in buffer (within time window), false otherwise
 *
 * @internal This function is for internal use only. Use isItInBuffer() for public API.
 *
 * @example
 * ```typescript
 * // Internal usage only
 * const inBuffer = isInBufferSync("ProcessPatientCreate:123");
 * if (inBuffer) {
 *   // Event was processed recently
 *   return;
 * }
 * ```
 */
function isInBufferSync(bufferKey: string): boolean {
	const now = Date.now();
	const bufferTime = Number(getConfig("syncBufferTimeSec")) * 1000; // Convert to milliseconds

	// Clean expired entries first
	cleanExpiredEntries(now, bufferTime);

	// Check if key exists and is still valid
	const timestamp = processingBuffer.get(bufferKey);
	if (timestamp && now - timestamp < bufferTime) {
		return true;
	}

	return false;
}

/**
 * Adds an event to the processing buffer with current timestamp
 *
 * This function marks an event as being processed by adding it to the buffer
 * with the current timestamp. This prevents duplicate processing of the same
 * event within the configured buffer time window.
 *
 * @param bufferKey - Unique key identifying the event to add to buffer
 *
 * @example
 * ```typescript
 * const bufferKey = generatePatientBufferKey("ProcessPatientCreate", patientId);
 *
 * // Check if already in buffer
 * if (await isItInBuffer(bufferKey)) {
 *   return { success: true, message: "Already processed" };
 * }
 *
 * // Add to buffer before processing
 * addToBuffer(bufferKey);
 *
 * // Process the event
 * await processPatientCreate(patientData);
 * ```
 */
export function addToBuffer(bufferKey: string): void {
	processingBuffer.set(bufferKey, Date.now());
}

/**
 * Removes an event from the processing buffer
 * This can be called when processing is complete, though it's not strictly necessary
 * as entries will expire automatically
 *
 * @param bufferKey - Unique key identifying the event
 */
export function removeFromBuffer(bufferKey: string): void {
	processingBuffer.delete(bufferKey);
}

/**
 * Cleans expired entries from the buffer
 * This is called automatically during buffer checks to prevent memory leaks
 *
 * @param now - Current timestamp
 * @param bufferTime - Buffer time in milliseconds
 */
function cleanExpiredEntries(now: number, bufferTime: number): void {
	for (const [key, timestamp] of processingBuffer.entries()) {
		if (now - timestamp >= bufferTime) {
			processingBuffer.delete(key);
		}
	}
}

/**
 * Gets the current size of the processing buffer
 * Useful for monitoring and debugging
 *
 * @returns Number of entries currently in the buffer
 */
export function getBufferSize(): number {
	return processingBuffer.size;
}

/**
 * Clears all entries from the processing buffer
 * Should only be used for testing or emergency cleanup
 */
export function clearBuffer(): void {
	processingBuffer.clear();
}

/**
 * Gets buffer statistics for monitoring
 *
 * @returns Object containing buffer statistics
 */
export function getBufferStats(): {
	size: number;
	oldestEntry: number | null;
	newestEntry: number | null;
} {
	let oldest: number | null = null;
	let newest: number | null = null;

	for (const timestamp of processingBuffer.values()) {
		if (oldest === null || timestamp < oldest) {
			oldest = timestamp;
		}
		if (newest === null || timestamp > newest) {
			newest = timestamp;
		}
	}

	return {
		size: processingBuffer.size,
		oldestEntry: oldest,
		newestEntry: newest,
	};
}

/**
 * Generates a buffer key for patient operations
 *
 * @param operation - Operation type (e.g., "ProcessPatientCreate", "ProcessPatientUpdate")
 * @param patientId - Patient ID (CC ID or AP ID)
 * @returns Buffer key string
 */
export function generatePatientBufferKey(
	operation: string,
	patientId: string | number,
): string {
	return `${operation}:${patientId}`;
}

/**
 * Generates a buffer key for appointment operations
 *
 * @param operation - Operation type (e.g., "ProcessAppointmentCreate", "ProcessAppointmentUpdate")
 * @param appointmentId - Appointment ID (CC ID or AP ID)
 * @returns Buffer key string
 */
export function generateAppointmentBufferKey(
	operation: string,
	appointmentId: string | number,
): string {
	return `${operation}:${appointmentId}`;
}

/**
 * Generates a buffer key for invoice/payment operations
 *
 * @param operation - Operation type (e.g., "ProcessInvoicePayment")
 * @param entityId - Entity ID
 * @returns Buffer key string
 */
export function generateInvoicePaymentBufferKey(
	operation: string,
	entityId: string | number,
): string {
	return `${operation}:${entityId}`;
}

/**
 * Helper function to check and add to buffer in one operation
 * This is the most common pattern - check if in buffer, and if not, add it
 *
 * @param bufferKey - Unique key identifying the event
 * @returns True if the event was already in buffer (should skip), false if added to buffer (should process)
 */
export function checkAndAddToBuffer(bufferKey: string): boolean {
	if (isInBufferSync(bufferKey)) {
		return true; // Already in buffer, should skip
	}

	addToBuffer(bufferKey);
	return false; // Not in buffer, added now, should process
}

/**
 * Timestamp-based buffer check for patient data
 * This method compares the payload's updatedAt timestamp with the local patient's ccUpdatedAt
 * Uses configurable tolerance buffer from config file
 *
 * @param payload - Patient payload from CC webhook
 * @param patient - Local patient record (optional, will be fetched if not provided)
 * @returns True if within buffer tolerance (should skip), false if outside buffer (should process)
 */
export async function isInBuffer(
	payload: GetCCPatientType,
	patient?: { ccUpdatedAt: string | null },
): Promise<boolean> {
	const payloadUpdatedAt = payload.updatedAt;
	let localUpdatedAt = patient?.ccUpdatedAt;

	// If no patient provided, fetch from database
	if (!patient && payload.id) {
		try {
			const db = getDb();
			const dbPatient = await db
				.select({ ccUpdatedAt: patientSchema.ccUpdatedAt })
				.from(patientSchema)
				.where(eq(patientSchema.ccId, payload.id))
				.limit(1)
				.then((results) => results[0]);

			localUpdatedAt = dbPatient?.ccUpdatedAt?.toISOString() || null;
		} catch (error) {
			console.error("Error fetching patient for buffer check:", error);
			return false; // If we can't fetch, assume not in buffer
		}
	}

	// If no local timestamp exists, not in buffer
	if (!localUpdatedAt) {
		return false;
	}

	// Convert payload timestamp to Date object
	const payloadDate = new Date(payloadUpdatedAt);
	const localDate = new Date(localUpdatedAt);

	// Calculate the absolute difference in milliseconds
	const timeDifferenceMs = Math.abs(
		payloadDate.getTime() - localDate.getTime(),
	);

	// Get configurable buffer time from config and convert to milliseconds
	const bufferTimeMs = Number(getConfig("syncBufferTimeSec")) * 1000;

	// Return true if within buffer tolerance, false if outside buffer
	return timeDifferenceMs <= bufferTimeMs;
}
