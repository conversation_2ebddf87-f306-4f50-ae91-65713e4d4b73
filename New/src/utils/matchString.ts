export const matchString = (value1: unknown, value2: unknown): boolean => {
	// Handle null/undefined cases
	if (value1 == null && value2 == null) return true;
	if (value1 == null || value2 == null) return false;

	// Convert any data type to string representation
	const getString = (value: unknown): string => {
		if (typeof value === "object") {
			try {
				return JSON.stringify(value);
			} catch {
				return String(value);
			}
		}
		return String(value);
	};

	// Clean strings by removing all non-alphanumeric characters and converting to lowercase
	const cleanString = (str: string): string => {
		return str.replace(/[^a-zA-Z0-9]/g, "").toLowerCase();
	};

	const cleanValue1 = cleanString(getString(value1));
	const cleanValue2 = cleanString(getString(value2));

	return cleanValue1 === cleanValue2;
};

export default matchString;
