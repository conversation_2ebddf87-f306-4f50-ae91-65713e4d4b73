import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { getBufferStats } from "./utils/bufferManager";
import { getPerformanceStats } from "./utils/performanceMonitor";
import { getApiStats } from "./api/optimizedClient";
import { getCacheStats } from "./database/optimizedQueries";
import {
	handleAPAppointmentWebhook,
	handleAPContactWebhook,
} from "./webhook/apHandler";
import { handleWebhookEvent } from "./webhook/handler";

const app = new Hono<Env>();
app.use(contextStorage());

app.onError((err, c) => {
	console.error(err);
	return c.json(
		{
			message: "Internal Server Error",
			requestId: crypto.randomUUID(),
			timestamp: new Date().toISOString(),
		},
		500,
	);
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) => {
	const performanceStats = getPerformanceStats();
	const apiStats = getApiStats();
	const cacheStats = getCacheStats();
	const bufferStats = getBufferStats();

	return c.json({
		status: "healthy",
		timestamp: new Date().toISOString(),
		version: "2.0.0",
		performance: {
			activeTimers: performanceStats.activeTimers,
			activeRequests: performanceStats.activeRequests,
			memoryUsage: performanceStats.memoryUsage,
		},
		api: {
			circuitBreakers: apiStats.circuitBreakers,
			activeRequests: apiStats.activeRequests,
			cacheSize: apiStats.cacheSize,
		},
		database: {
			cacheSize: cacheStats.size,
			cacheMemoryUsage: cacheStats.memoryUsage,
		},
		buffer: bufferStats,
	});
});

// Webhook endpoint for receiving data synchronization events from CC
// Replaces socket.io events with HTTP webhooks
app.post("/webhook", handleWebhookEvent);

// AP webhook endpoints for bi-directional sync (AP → CC)
// Consolidated endpoints that auto-detect create/update operations
// Location is now retrieved from config file instead of URL parameter
app.post("/ap/appointment", handleAPAppointmentWebhook);
app.post("/ap/contact", handleAPContactWebhook);

// Sync services endpoint (from v3Integration - basic implementation)
app.post("/sync-services", (c) => {
	return c.json({
		message: "Sync services endpoint - functionality to be implemented",
		status: "ok",
		timestamp: new Date().toISOString(),
	});
});

export default app;
